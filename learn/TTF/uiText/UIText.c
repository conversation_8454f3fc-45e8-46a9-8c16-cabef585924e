#include "UIText.h"
#include <float.h>

// Shaders
#include "../testgputext/shaders/shader.vert.spv.h"
#include "../testgputext/shaders/shader.frag.spv.h"
#include "../testgputext/shaders/shader-sdf.frag.spv.h"
#include "../testgputext/shaders/shader.vert.dxil.h"
#include "../testgputext/shaders/shader.frag.dxil.h"
#include "../testgputext/shaders/shader-sdf.frag.dxil.h"
#include "../testgputext/shaders/shader.vert.msl.h"
#include "../testgputext/shaders/shader.frag.msl.h"
#include "../testgputext/shaders/shader-sdf.frag.msl.h"

#define SUPPORTED_SHADER_FORMATS (SDL_GPU_SHADERFORMAT_SPIRV | SDL_GPU_SHADERFORMAT_DXIL | SDL_GPU_SHADERFORMAT_MSL)

typedef enum
{
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
    <PERSON><PERSON>l<PERSON>hader,
    <PERSON>xel<PERSON>hader_SDF,
} Shader;

// Error checking helpers


static void *check_error_ptr(void *ptr)
{
    if (!ptr)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "%s", SDL_GetError());
    }
    return ptr;
}

// Shader loading function
static SDL_GPUShader *load_shader(
        SDL_GPUDevice *device,
        Shader shader,
        Uint32 sampler_count,
        Uint32 uniform_buffer_count,
        Uint32 storage_buffer_count,
        Uint32 storage_texture_count)
{
    SDL_GPUShaderCreateInfo createinfo;
    createinfo.num_samplers = sampler_count;
    createinfo.num_storage_buffers = storage_buffer_count;
    createinfo.num_storage_textures = storage_texture_count;
    createinfo.num_uniform_buffers = uniform_buffer_count;
    createinfo.props = 0;

    SDL_GPUShaderFormat format = SDL_GetGPUShaderFormats(device);
    if (format & SDL_GPU_SHADERFORMAT_DXIL)
    {
        createinfo.format = SDL_GPU_SHADERFORMAT_DXIL;
        switch (shader)
        {
        case VertexShader:
            createinfo.code = shader_vert_dxil;
            createinfo.code_size = shader_vert_dxil_len;
            createinfo.entrypoint = "VSMain";
            break;
        case PixelShader:
            createinfo.code = shader_frag_dxil;
            createinfo.code_size = shader_frag_dxil_len;
            createinfo.entrypoint = "PSMain";
            break;
        case PixelShader_SDF:
            createinfo.code = shader_sdf_frag_dxil;
            createinfo.code_size = shader_sdf_frag_dxil_len;
            createinfo.entrypoint = "PSMain";
            break;
        }
    }
    else if (format & SDL_GPU_SHADERFORMAT_MSL)
    {
        createinfo.format = SDL_GPU_SHADERFORMAT_MSL;
        switch (shader)
        {
        case VertexShader:
            createinfo.code = shader_vert_msl;
            createinfo.code_size = shader_vert_msl_len;
            createinfo.entrypoint = "main0";
            break;
        case PixelShader:
            createinfo.code = shader_frag_msl;
            createinfo.code_size = shader_frag_msl_len;
            createinfo.entrypoint = "main0";
            break;
        case PixelShader_SDF:
            createinfo.code = shader_sdf_frag_msl;
            createinfo.code_size = shader_sdf_frag_msl_len;
            createinfo.entrypoint = "main0";
            break;
        }
    }
    else
    {
        createinfo.format = SDL_GPU_SHADERFORMAT_SPIRV;
        switch (shader)
        {
        case VertexShader:
            createinfo.code = shader_vert_spv;
            createinfo.code_size = shader_vert_spv_len;
            createinfo.entrypoint = "main";
            break;
        case PixelShader:
            createinfo.code = shader_frag_spv;
            createinfo.code_size = shader_frag_spv_len;
            createinfo.entrypoint = "main";
            break;
        case PixelShader_SDF:
            createinfo.code = shader_sdf_frag_spv;
            createinfo.code_size = shader_sdf_frag_spv_len;
            createinfo.entrypoint = "main";
            break;
        }
    }

    if (shader == VertexShader)
    {
        createinfo.stage = SDL_GPU_SHADERSTAGE_VERTEX;
    }
    else
    {
        createinfo.stage = SDL_GPU_SHADERSTAGE_FRAGMENT;
    }
    return SDL_CreateGPUShader(device, &createinfo);
}





//Copies accumulated text geometry data from CPU memory to GPU transfer buffer.
static void set_geometry_data(UITextContext *context)
{
    Vertex *transfer_data = SDL_MapGPUTransferBuffer(context->device, context->transfer_buffer, false);
    SDL_memcpy(transfer_data, context->geometry_data.vertices, sizeof(Vertex) * context->geometry_data.vertex_count);
    SDL_memcpy(transfer_data + MAX_VERTEX_COUNT, context->geometry_data.indices, sizeof(int) * context->geometry_data.index_count);
    SDL_UnmapGPUTransferBuffer(context->device, context->transfer_buffer);
}
/**
 * Transfers geometry data from transfer buffer to GPU vertex/index buffers.
 *
 * This function performs the actual GPU memory transfer operation by:
 * 1. Beginning a GPU copy pass (required for all GPU-to-GPU memory operations)
 * 2. Uploading vertex data from transfer buffer to the dedicated vertex buffer
 * 3. Uploading index data from transfer buffer to the dedicated index buffer
 * 4. Ending the copy pass to commit the transfers
 *
 * Transfer operations:
 * - Vertex data: transfer_buffer[0] → vertex_buffer (positions, colors, UVs)
 * - Index data: transfer_buffer[MAX_VERTEX_COUNT offset] → index_buffer (triangle indices)
 *
 * IMPORTANT: This function MUST be called outside of any active render pass!
 * Copy passes and render passes cannot be active simultaneously on the same command buffer.
 *
 * Memory flow: CPU → transfer_buffer (via set_geometry_data) → GPU buffers (via this function)
 *
 * @param context UIText context containing transfer buffer and target GPU buffers
 * @param cmd_buf Command buffer to record the copy operations
 */
static void transfer_data(UITextContext *context, SDL_GPUCommandBuffer *cmd_buf)
{
    SDL_GPUCopyPass *copy_pass = check_error_ptr(SDL_BeginGPUCopyPass(cmd_buf));
    SDL_UploadToGPUBuffer(
            copy_pass,
            &(SDL_GPUTransferBufferLocation){.transfer_buffer = context->transfer_buffer, .offset = 0},
            &(SDL_GPUBufferRegion){.buffer = context->vertex_buffer, .offset = 0, .size = sizeof(Vertex) * context->geometry_data.vertex_count},
            false);
    SDL_UploadToGPUBuffer(
            copy_pass,
            &(SDL_GPUTransferBufferLocation){.transfer_buffer = context->transfer_buffer, .offset = sizeof(Vertex) * MAX_VERTEX_COUNT},
            &(SDL_GPUBufferRegion){.buffer = context->index_buffer, .offset = 0, .size = sizeof(int) * context->geometry_data.index_count},
            false);
    SDL_EndGPUCopyPass(copy_pass);
}



// Public API functions
UITextContext* UIText_CreateContext(SDL_GPUDevice *device, SDL_Window *window, int window_width, int window_height, bool use_SDF)
{
    UITextContext *context = SDL_calloc(1, sizeof(UITextContext));
    if (!context)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate UITextContext");
        return NULL;
    }

    context->device = device;
    context->window_width = window_width;
    context->window_height = window_height;
    context->use_SDF = use_SDF;

    // Load shaders
    SDL_GPUShader *vertex_shader = check_error_ptr(load_shader(device, VertexShader, 0, 1, 0, 0));
    SDL_GPUShader *fragment_shader = check_error_ptr(load_shader(device, use_SDF ? PixelShader_SDF : PixelShader, 1, 0, 0, 0));

    if (!vertex_shader || !fragment_shader)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to load shaders");
        SDL_free(context);
        return NULL;
    }

    // Create graphics pipeline
    SDL_GPUGraphicsPipelineCreateInfo pipeline_create_info = {
            .target_info = {
                    .num_color_targets = 1,
                    .color_target_descriptions = (SDL_GPUColorTargetDescription[]){
                            {//
                             .format = SDL_GetGPUSwapchainTextureFormat(device, window),
                             .blend_state = (SDL_GPUColorTargetBlendState){//
                                     .enable_blend = true,
                                     .alpha_blend_op = SDL_GPU_BLENDOP_ADD,
                                     .color_blend_op = SDL_GPU_BLENDOP_ADD,
                                     .color_write_mask = 0xF,
                                     .src_alpha_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                                     .dst_alpha_blendfactor = SDL_GPU_BLENDFACTOR_DST_ALPHA,
                                     .src_color_blendfactor = SDL_GPU_BLENDFACTOR_SRC_ALPHA,
                                     .dst_color_blendfactor = SDL_GPU_BLENDFACTOR_ONE_MINUS_SRC_ALPHA}//
														} //
                    },
                    .has_depth_stencil_target = false,
                    .depth_stencil_format = SDL_GPU_TEXTUREFORMAT_INVALID},
            .vertex_input_state = (SDL_GPUVertexInputState){
                    .num_vertex_buffers = 1,
                    .vertex_buffer_descriptions = (SDL_GPUVertexBufferDescription[]){
                            {//
                             .slot = 0,
                             .input_rate = SDL_GPU_VERTEXINPUTRATE_VERTEX,
                             .pitch = sizeof(Vertex)} //
                    },
                    .num_vertex_attributes = 3, //
                    .vertex_attributes = (SDL_GPUVertexAttribute[]){
                            {//
                             .buffer_slot = 0,
                             .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT3,
                             .location = 0,
                             .offset = 0},
                            {//
                             .buffer_slot = 0,
                             .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT4,
                             .location = 1,
                             .offset = sizeof(float) * 3},
                            {//
                             .buffer_slot = 0,
                             .format = SDL_GPU_VERTEXELEMENTFORMAT_FLOAT2,
                             .location = 2,
                             .offset = sizeof(float) * 7} //
                    } //
            },
            .primitive_type = SDL_GPU_PRIMITIVETYPE_TRIANGLELIST,
            .vertex_shader = vertex_shader,
            .fragment_shader = fragment_shader};
    context->pipeline = check_error_ptr(SDL_CreateGPUGraphicsPipeline(device, &pipeline_create_info));

    SDL_ReleaseGPUShader(device, vertex_shader);
    SDL_ReleaseGPUShader(device, fragment_shader);

    if (!context->pipeline)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create graphics pipeline");
        SDL_free(context);
        return NULL;
    }

    // Create buffers
    context->vertex_buffer = check_error_ptr(SDL_CreateGPUBuffer(device, &(SDL_GPUBufferCreateInfo){.usage = SDL_GPU_BUFFERUSAGE_VERTEX, .size = sizeof(Vertex) * MAX_VERTEX_COUNT}));
    context->index_buffer = check_error_ptr(SDL_CreateGPUBuffer(device, &(SDL_GPUBufferCreateInfo){.usage = SDL_GPU_BUFFERUSAGE_INDEX, .size = sizeof(int) * MAX_INDEX_COUNT}));
    context->transfer_buffer = check_error_ptr(SDL_CreateGPUTransferBuffer(device, &(SDL_GPUTransferBufferCreateInfo){.usage = SDL_GPU_TRANSFERBUFFERUSAGE_UPLOAD, .size = (sizeof(Vertex) * MAX_VERTEX_COUNT) + (sizeof(int) * MAX_INDEX_COUNT)}));
    context->sampler = check_error_ptr(SDL_CreateGPUSampler(device, &(SDL_GPUSamplerCreateInfo){.min_filter = SDL_GPU_FILTER_LINEAR, .mag_filter = SDL_GPU_FILTER_LINEAR, .mipmap_mode = SDL_GPU_SAMPLERMIPMAPMODE_LINEAR, .address_mode_u = SDL_GPU_SAMPLERADDRESSMODE_CLAMP_TO_EDGE, .address_mode_v = SDL_GPU_SAMPLERADDRESSMODE_CLAMP_TO_EDGE, .address_mode_w = SDL_GPU_SAMPLERADDRESSMODE_CLAMP_TO_EDGE}));

    if (!context->vertex_buffer || !context->index_buffer || !context->transfer_buffer || !context->sampler)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create GPU resources");
        UIText_DestroyContext(context);
        return NULL;
    }

    // Allocate geometry data
    context->geometry_data.vertices = SDL_calloc(MAX_VERTEX_COUNT, sizeof(Vertex));
    context->geometry_data.indices = SDL_calloc(MAX_INDEX_COUNT, sizeof(int));

    if (!context->geometry_data.vertices || !context->geometry_data.indices)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate geometry data");
        UIText_DestroyContext(context);
        return NULL;
    }

    // Initialize TTF engine
    context->engine = check_error_ptr(TTF_CreateGPUTextEngine(device));
    if (!context->engine)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create TTF GPU text engine");
        UIText_DestroyContext(context);
        return NULL;
    }

    // Setup orthographic projection matrix for 2D rendering
    UIText_UpdateProjectionMatrix(context, window_width, window_height);

    return context;
}

void UIText_DestroyContext(UITextContext *context)
{
    if (!context)
        return;

    if (context->geometry_data.vertices)
        SDL_free(context->geometry_data.vertices);
    if (context->geometry_data.indices)
        SDL_free(context->geometry_data.indices);

    if (context->engine)
        TTF_DestroyGPUTextEngine(context->engine);

    if (context->device)
    {
        if (context->transfer_buffer)
            SDL_ReleaseGPUTransferBuffer(context->device, context->transfer_buffer);
        if (context->sampler)
            SDL_ReleaseGPUSampler(context->device, context->sampler);
        if (context->vertex_buffer)
            SDL_ReleaseGPUBuffer(context->device, context->vertex_buffer);
        if (context->index_buffer)
            SDL_ReleaseGPUBuffer(context->device, context->index_buffer);
        if (context->pipeline)
            SDL_ReleaseGPUGraphicsPipeline(context->device, context->pipeline);
    }

    SDL_free(context);
}

UITextObject* UIText_CreateText(UITextContext *context, TTF_Font *font, const char *text_string, float x, float y, SDL_FColor colour)
{
    if (!context || !font || !text_string)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Invalid parameters for UIText_CreateText");
        return NULL;
    }

    UITextObject *text_obj = SDL_calloc(1, sizeof(UITextObject));
    if (!text_obj)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate UITextObject");
        return NULL;
    }

    // Set SDF mode on the font to match context
    TTF_SetFontSDF(font, context->use_SDF);

    // Create the text object at origin (0,0)
    text_obj->text = check_error_ptr(TTF_CreateText(context->engine, font, text_string, 0));
    if (!text_obj->text)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to create TTF text");
        SDL_free(text_obj);
        return NULL;
    }

    text_obj->font = font;
    text_obj->colour = colour;
    text_obj->x = x;
    text_obj->y = y;
    text_obj->letter_spacing = 1.0f; // Default: normal spacing

    return text_obj;
}

void UIText_DestroyText(UITextObject *text_obj)
{
    if (!text_obj)
        return;

    if (text_obj->text)
        TTF_DestroyText(text_obj->text);

    SDL_free(text_obj);
}


// Prepare text data for rendering (call outside render pass)
void UIText_PrepareTextData(UITextContext *context, UITextObject **text_objects, int count, SDL_GPUCommandBuffer *cmd_buf)
{
    if (!context || !text_objects || count <= 0 || !cmd_buf)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Invalid parameters for UIText_PrepareTextData");
        return;
    }

    // Set up projection matrix for bottom-left origin coordinate system (required for TTF text rendering)
    // TTF text glyphs are designed for bottom-left origin, so we use the standard OpenGL projection
    context->projection_matrix = SDL_MatrixOrtho(0.0f, (float)context->window_width, 0.0f, (float)context->window_height, -1.0f, 1.0f);

    // Create individual model matrices for each text object
    context->model_matrix_count = 0;
    for (int i = 0; i < count && i < (MAX_VERTEX_COUNT / 4); i++)
    {
        UITextObject *text_obj = text_objects[i];
        if (!text_obj || !text_obj->text)
            continue;

        // Create model matrix with text object position
        // Convert from top-left UI coordinates to bottom-left coordinates for TTF rendering
        // In top-left: y=0 is top, y=height is bottom
        // In bottom-left: y=0 is bottom, y=height is top
        // So: bottom_left_y = window_height - top_left_y
        float converted_y = (float)context->window_height - text_obj->y;
        SDL_Mat4X4 model = SDL_MatrixTranslation((SDL_Vec3){text_obj->x, converted_y, 0.0f});

        context->model_matrices[context->model_matrix_count] = model;
        context->model_matrix_count++;
    }

    // Reset geometry data once for all texts
    context->geometry_data.vertex_count = 0;
    context->geometry_data.index_count = 0;

    // Collect geometry from all text objects
    for (int i = 0; i < count; i++)
    {
        UITextObject *text_obj = text_objects[i];
        if (!text_obj || !text_obj->text)
            continue;

        // Get the draw data for this text
        TTF_GPUAtlasDrawSequence *draw_sequence = TTF_GetGPUTextDrawData(text_obj->text);
        if (!draw_sequence)
            continue;

        // Proper letter spacing: adjust spacing between characters while preserving character shapes
        // This approach uses TTF_GetGlyphMetrics to get accurate character advance widths
        // and applies spacing adjustments based on the actual character metrics

        // Get the original text string from the TTF_Text object
        const char *text_string = text_obj->text->text;
        if (!text_string || !*text_string) {
            continue; // Skip empty text
        }

        // Count total characters and calculate character advance widths
        int text_length = SDL_strlen(text_string);
        if (text_length == 0) continue;

        // Array to store character advance widths and cumulative spacing offsets
        typedef struct {
            int advance_width;     // Original advance width from TTF_GetGlyphMetrics
            float spacing_offset;  // Cumulative spacing offset to apply
        } CharacterSpacing;

        CharacterSpacing *char_spacing = SDL_malloc(text_length * sizeof(CharacterSpacing));
        if (!char_spacing) {
            SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Failed to allocate character spacing array");
            continue;
        }

        // Get advance width for each character using TTF_GetGlyphMetrics
        float cumulative_offset = 0.0f;
        const char *text_ptr = text_string;
        for (int i = 0; i < text_length && *text_ptr; i++) {
            Uint32 codepoint = SDL_StepUTF8(&text_ptr, NULL);

            // Get glyph metrics for this character
            int advance_width = 0;
            if (!TTF_GetGlyphMetrics(text_obj->font, codepoint, NULL, NULL, NULL, NULL, &advance_width)) {
                // If we can't get metrics, use a default advance
                advance_width = 10; // Fallback width
            }

            char_spacing[i].advance_width = advance_width;
            char_spacing[i].spacing_offset = cumulative_offset;

            // Calculate spacing adjustment for next character
            if (i < text_length - 1) { // Don't add spacing after the last character
                float base_advance = (float)advance_width;
                float adjusted_advance = base_advance * text_obj->letter_spacing;
                float spacing_delta = adjusted_advance - base_advance;
                cumulative_offset += spacing_delta;
            }
        }

        // Apply spacing offsets to vertices
        // Each character is represented by 4 vertices (forming a quad)
        int char_index = 0;
        for (TTF_GPUAtlasDrawSequence *seq = draw_sequence; seq != NULL; seq = seq->next)
        {
            int num_chars_in_seq = seq->num_vertices / 4;
            for (int c = 0; c < num_chars_in_seq && char_index < text_length; c++) {
                // Get the spacing offset for this character
                float x_offset = char_spacing[char_index].spacing_offset;

                // Apply the offset to all 4 vertices of this character
                int vertex_base = c * 4;
                for (int v = 0; v < 4; v++) {
                    Vertex vert;
                    const SDL_FPoint pos = seq->xy[vertex_base + v];

                    // Apply character spacing offset (preserves character shape)
                    vert.pos = (Vec3){pos.x + x_offset, pos.y, 0.0f};
                    vert.colour = text_obj->colour;
                    vert.uv = seq->uv[vertex_base + v];

                    context->geometry_data.vertices[context->geometry_data.vertex_count + vertex_base + v] = vert;
                }
                char_index++;
            }

            SDL_memcpy(context->geometry_data.indices + context->geometry_data.index_count,
                      seq->indices, seq->num_indices * sizeof(int));

            context->geometry_data.vertex_count += seq->num_vertices;
            context->geometry_data.index_count += seq->num_indices;
        }

        // Clean up allocated memory
        SDL_free(char_spacing);
    }

    // Transfer data to GPU (must be done outside render pass)
    if (context->geometry_data.vertex_count > 0)
    {
        set_geometry_data(context);
        transfer_data(context, cmd_buf);
    }
}

// Render prepared text within an existing render pass
void UIText_RenderInRenderPass(UITextContext *context, UITextObject **text_objects, int count,
                               SDL_GPURenderPass *render_pass, SDL_GPUCommandBuffer *cmd_buf)
{
    if (!context || !text_objects || count <= 0 || !render_pass || !cmd_buf)
    {
        SDL_LogError(SDL_LOG_CATEGORY_APPLICATION, "Invalid parameters for UIText_RenderInRenderPass");
        return;
    }

    // Only render if we have geometry data
    if (context->geometry_data.vertex_count <= 0)
    {
        return;
    }

    // Bind text rendering pipeline and resources
    SDL_BindGPUGraphicsPipeline(render_pass, context->pipeline);
    SDL_BindGPUVertexBuffers(render_pass, 0, &(SDL_GPUBufferBinding){.buffer = context->vertex_buffer, .offset = 0}, 1);
    SDL_BindGPUIndexBuffer(render_pass, &(SDL_GPUBufferBinding){.buffer = context->index_buffer, .offset = 0}, SDL_GPU_INDEXELEMENTSIZE_32BIT);

    // Draw each text object with its individual model matrix
    int index_offset = 0, vertex_offset = 0;
    int matrix_index = 0;

    for (int i = 0; i < count; i++)
    {
        UITextObject *text_obj = text_objects[i];
        if (!text_obj || !text_obj->text)
            continue;

        TTF_GPUAtlasDrawSequence *draw_sequence = TTF_GetGPUTextDrawData(text_obj->text);
        if (!draw_sequence)
            continue;

        // Push individual matrices for this text object (projection + model)
        if (matrix_index < context->model_matrix_count) {
            SDL_Mat4X4 matrices[2] = {
                context->projection_matrix,
                context->model_matrices[matrix_index]
            };
            SDL_PushGPUVertexUniformData(cmd_buf, 0, matrices, sizeof(SDL_Mat4X4) * 2);
            matrix_index++;
        }

        for (TTF_GPUAtlasDrawSequence *seq = draw_sequence; seq != NULL; seq = seq->next)
        {
            SDL_BindGPUFragmentSamplers(
                    render_pass, 0,
                    &(SDL_GPUTextureSamplerBinding){
                            .texture = seq->atlas_texture,
                            .sampler = context->sampler},
                    1);
            SDL_DrawGPUIndexedPrimitives(render_pass, seq->num_indices, 1, index_offset, vertex_offset, 0);
            index_offset += seq->num_indices;
            vertex_offset += seq->num_vertices;
        }
    }
}



// Utility functions
void UIText_SetTextPosition(UITextObject *text_obj, float x, float y)
{
    if (text_obj)
    {
        text_obj->x = x;
        text_obj->y = y;
    }
}

void UIText_SetTextColour(UITextObject *text_obj, SDL_FColor colour)
{
    if (text_obj)
    {
        text_obj->colour = colour;
    }
}

void UIText_SetLetterSpacing(UITextObject *text_obj, float spacing)
{
    if (text_obj)
    {
        text_obj->letter_spacing = spacing;
    }
}

void UIText_UpdateProjectionMatrix(UITextContext *context, int window_width, int window_height)
{
    if (!context)
        return;

    context->window_width = window_width;
    context->window_height = window_height;

    // Setup orthographic projection matrix for 2D rendering with top-left origin
    // For top-left origin: left=0, right=width, top=0, bottom=height
    context->projection_matrix = SDL_MatrixOrtho(0.0f, (float)window_width, (float)window_height, 0.0f, -1.0f, 1.0f);
}